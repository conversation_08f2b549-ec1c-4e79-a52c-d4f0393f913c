package com.alpha.gallery

import android.app.Application
import com.alpha.gallery.core.sync.initializer.SyncInitializer
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class GalleryApplication : Application() {

    @Inject
    lateinit var syncInitializer: SyncInitializer

    override fun onCreate() {
        super.onCreate()

        // Initialize sync system
        syncInitializer.initialize()
    }
}
