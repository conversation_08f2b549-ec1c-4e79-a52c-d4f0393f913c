package com.alpha.gallery.data.local.repository

import android.util.Log
import com.alpha.gallery.core.database.dao.MediaItemDao
import com.alpha.gallery.core.database.mapper.MediaItemMapper.toDomainModel
import com.alpha.gallery.core.database.mapper.MediaItemMapper.toDomainModels
import com.alpha.gallery.core.domain.model.Album
import com.alpha.gallery.core.domain.model.MediaItem
import com.alpha.gallery.core.domain.repository.MediaRepository
import com.alpha.gallery.core.sync.manager.SyncManager
import com.alpha.gallery.core.sync.scheduler.SyncScheduler
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of MediaRepository that coordinates local database and sync operations
 */
@Singleton
class MediaRepositoryImpl @Inject constructor(
    private val mediaItemDao: MediaItemDao,
    private val syncManager: SyncManager,
    private val syncScheduler: SyncScheduler
) : MediaRepository {
    
    companion object {
        private const val TAG = "MediaRepositoryImpl"
    }
    
    override fun getAllMediaItems(): Flow<List<MediaItem>> {
        return mediaItemDao.getAllMediaItems().map { entities ->
            entities.toDomainModels()
        }
    }
    
    override fun getMediaItemsByAlbum(albumId: String): Flow<List<MediaItem>> {
        return mediaItemDao.getMediaItemsByAlbum(albumId).map { entities ->
            entities.toDomainModels()
        }
    }
    
    override suspend fun getMediaItemById(id: String): MediaItem? {
        return try {
            val mediaStoreId = id.toLongOrNull() ?: return null
            mediaItemDao.getMediaItemByMediaStoreId(mediaStoreId)?.toDomainModel()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting media item by id: $id", e)
            null
        }
    }
    
    override fun getAllAlbums(): Flow<List<Album>> {
        // TODO: Implement album aggregation from media items
        // For now, return empty flow as albums are derived from media items
        return kotlinx.coroutines.flow.flowOf(emptyList())
    }
    
    override suspend fun getAlbumById(id: String): Album? {
        // TODO: Implement album retrieval
        return null
    }
    
    override fun searchMediaItems(query: String): Flow<List<MediaItem>> {
        return mediaItemDao.searchMediaItems(query).map { entities ->
            entities.toDomainModels()
        }
    }
    
    override fun getRecentMediaItems(limit: Int): Flow<List<MediaItem>> {
        return mediaItemDao.getRecentMediaItems(limit).map { entities ->
            entities.toDomainModels()
        }
    }
    
    override fun getFavoriteMediaItems(): Flow<List<MediaItem>> {
        return mediaItemDao.getFavoriteMediaItems().map { entities ->
            entities.toDomainModels()
        }
    }
    
    override suspend fun addToFavorites(mediaItemId: String) {
        try {
            val mediaStoreId = mediaItemId.toLongOrNull() ?: return
            mediaItemDao.updateFavoriteStatus(mediaStoreId, true)
            Log.d(TAG, "Added item $mediaItemId to favorites")
        } catch (e: Exception) {
            Log.e(TAG, "Error adding item to favorites: $mediaItemId", e)
        }
    }
    
    override suspend fun removeFromFavorites(mediaItemId: String) {
        try {
            val mediaStoreId = mediaItemId.toLongOrNull() ?: return
            mediaItemDao.updateFavoriteStatus(mediaStoreId, false)
            Log.d(TAG, "Removed item $mediaItemId from favorites")
        } catch (e: Exception) {
            Log.e(TAG, "Error removing item from favorites: $mediaItemId", e)
        }
    }
    
    override suspend fun deleteMediaItem(mediaItemId: String): Boolean {
        return try {
            val mediaStoreId = mediaItemId.toLongOrNull() ?: return false
            val deletedCount = mediaItemDao.deleteByMediaStoreId(mediaStoreId)
            Log.d(TAG, "Deleted item $mediaItemId from database")
            deletedCount > 0
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting media item: $mediaItemId", e)
            false
        }
    }
    
    override suspend fun refreshMediaItems() {
        Log.d(TAG, "Refreshing media items")
        try {
            syncScheduler.scheduleImmediateSync(forceFullSync = false)
        } catch (e: Exception) {
            Log.e(TAG, "Error refreshing media items", e)
        }
    }
    
    override suspend fun syncWithCloud() {
        // TODO: Implement cloud sync when cloud storage is added
        Log.d(TAG, "Cloud sync not implemented yet")
    }
    
    /**
     * Perform full sync of media items
     */
    suspend fun performFullSync(): SyncManager.SyncResult {
        Log.d(TAG, "Performing full sync")
        return syncManager.performFullSync()
    }
    
    /**
     * Get sync status
     */
    suspend fun getSyncStatus(): SyncManager.SyncStatus {
        return syncManager.getSyncStatus()
    }
    
    /**
     * Get total media count
     */
    suspend fun getMediaCount(): Int {
        return try {
            mediaItemDao.getMediaItemCount()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting media count", e)
            0
        }
    }
    
    /**
     * Get image count
     */
    suspend fun getImageCount(): Int {
        return try {
            mediaItemDao.getCountByMimeType("image")
        } catch (e: Exception) {
            Log.e(TAG, "Error getting image count", e)
            0
        }
    }
    
    /**
     * Get video count
     */
    suspend fun getVideoCount(): Int {
        return try {
            mediaItemDao.getCountByMimeType("video")
        } catch (e: Exception) {
            Log.e(TAG, "Error getting video count", e)
            0
        }
    }
}
