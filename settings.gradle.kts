pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.name = "Gallery"
include(":app")

// Core modules
include(":core:common")
include(":core:data")
include(":core:domain")
include(":core:ui")
include(":core:network")
include(":core:database")

// Feature modules
include(":feature:gallery")
include(":feature:media-viewer")
include(":feature:albums")
include(":feature:cloud")
include(":feature:settings")

// Data modules
include(":data:local")
include(":data:remote")
