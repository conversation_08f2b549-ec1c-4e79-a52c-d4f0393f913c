package com.alpha.gallery.core.common

object Constants {
    // Database
    const val DATABASE_NAME = "gallery_database"
    const val DATABASE_VERSION = 1
    
    // Network
    const val NETWORK_TIMEOUT = 30L
    const val CONNECT_TIMEOUT = 30L
    const val READ_TIMEOUT = 30L
    
    // Media
    const val SUPPORTED_IMAGE_EXTENSIONS = "jpg,jpeg,png,gif,webp,bmp"
    const val SUPPORTED_VIDEO_EXTENSIONS = "mp4,avi,mov,mkv,wmv,flv,webm"
    
    // Preferences
    const val PREFERENCES_NAME = "gallery_preferences"
    
    // Grid
    const val DEFAULT_GRID_COLUMNS = 3
    const val MIN_GRID_COLUMNS = 2
    const val MAX_GRID_COLUMNS = 6
    
    // Cache
    const val IMAGE_CACHE_SIZE = 100 * 1024 * 1024L // 100MB
    const val THUMBNAIL_CACHE_SIZE = 50 * 1024 * 1024L // 50MB
    
    // Permissions
    const val PERMISSION_REQUEST_CODE = 1001
}
