package com.alpha.gallery.core.common

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Extension function to convert Flow to Result Flow
 */
fun <T> Flow<T>.asResult(): Flow<Result<T>> = this
    .map<T, Result<T>> { Result.Success(it) }
    .onStart { emit(Result.Loading) }
    .catch { emit(Result.Error(it)) }

/**
 * Extension function to format file size
 */
fun Long.formatFileSize(): String {
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    var size = this.toDouble()
    var unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.size - 1) {
        size /= 1024
        unitIndex++
    }
    
    return String.format(Locale.getDefault(), "%.1f %s", size, units[unitIndex])
}

/**
 * Extension function to format date
 */
fun Long.formatDate(pattern: String = "yyyy-MM-dd HH:mm"): String {
    val formatter = SimpleDateFormat(pattern, Locale.getDefault())
    return formatter.format(Date(this))
}

/**
 * Extension function to check if string is image file
 */
fun String.isImageFile(): Boolean {
    val extension = this.substringAfterLast('.', "").lowercase()
    return Constants.SUPPORTED_IMAGE_EXTENSIONS.split(',').contains(extension)
}

/**
 * Extension function to check if string is video file
 */
fun String.isVideoFile(): Boolean {
    val extension = this.substringAfterLast('.', "").lowercase()
    return Constants.SUPPORTED_VIDEO_EXTENSIONS.split(',').contains(extension)
}

/**
 * Extension function to check if string is media file
 */
fun String.isMediaFile(): Boolean = isImageFile() || isVideoFile()

/**
 * Extension function to get file extension
 */
fun String.getFileExtension(): String = this.substringAfterLast('.', "").lowercase()

/**
 * Extension function to get file name without extension
 */
fun String.getFileNameWithoutExtension(): String = this.substringBeforeLast('.')
