package com.alpha.gallery.core.domain.usecase

import com.alpha.gallery.core.common.Result
import com.alpha.gallery.core.common.asResult
import com.alpha.gallery.core.domain.model.MediaItem
import com.alpha.gallery.core.domain.repository.MediaRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case to get all media items
 */
class GetAllMediaItemsUseCase @Inject constructor(
    private val mediaRepository: MediaRepository
) {
    operator fun invoke(): Flow<Result<List<MediaItem>>> {
        return mediaRepository.getAllMediaItems().asResult()
    }
}
