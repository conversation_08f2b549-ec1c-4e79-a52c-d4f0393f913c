package com.alpha.gallery.core.domain.model

import kotlinx.serialization.Serializable

/**
 * Domain model for albums/folders
 */
@Serializable
data class Album(
    val id: String,
    val name: String,
    val path: String,
    val coverImagePath: String? = null,
    val mediaCount: Int = 0,
    val dateAdded: Long,
    val dateModified: Long,
    val isCloudAlbum: Boolean = false
) {
    val displayName: String
        get() = if (name.isBlank()) "Unknown Album" else name
}
