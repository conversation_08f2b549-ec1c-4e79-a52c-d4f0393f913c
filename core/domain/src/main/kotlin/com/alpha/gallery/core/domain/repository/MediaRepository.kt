package com.alpha.gallery.core.domain.repository

import com.alpha.gallery.core.domain.model.Album
import com.alpha.gallery.core.domain.model.MediaItem
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for media operations
 */
interface MediaRepository {
    
    /**
     * Get all media items
     */
    fun getAllMediaItems(): Flow<List<MediaItem>>
    
    /**
     * Get media items by album
     */
    fun getMediaItemsByAlbum(albumId: String): Flow<List<MediaItem>>
    
    /**
     * Get media item by id
     */
    suspend fun getMediaItemById(id: String): MediaItem?
    
    /**
     * Get all albums
     */
    fun getAllAlbums(): Flow<List<Album>>
    
    /**
     * Get album by id
     */
    suspend fun getAlbumById(id: String): Album?
    
    /**
     * Search media items
     */
    fun searchMediaItems(query: String): Flow<List<MediaItem>>
    
    /**
     * Get recent media items
     */
    fun getRecentMediaItems(limit: Int = 50): Flow<List<MediaItem>>
    
    /**
     * Get favorite media items
     */
    fun getFavoriteMediaItems(): Flow<List<MediaItem>>
    
    /**
     * Add media item to favorites
     */
    suspend fun addToFavorites(mediaItemId: String)
    
    /**
     * Remove media item from favorites
     */
    suspend fun removeFromFavorites(mediaItemId: String)
    
    /**
     * Delete media item
     */
    suspend fun deleteMediaItem(mediaItemId: String): Boolean
    
    /**
     * Refresh media items from device storage
     */
    suspend fun refreshMediaItems()
    
    /**
     * Sync with cloud storage
     */
    suspend fun syncWithCloud()
}
