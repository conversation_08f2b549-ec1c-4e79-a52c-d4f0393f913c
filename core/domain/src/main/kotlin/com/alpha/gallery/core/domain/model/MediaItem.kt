package com.alpha.gallery.core.domain.model

import kotlinx.serialization.Serializable

/**
 * Domain model for media items (images and videos)
 */
@Serializable
data class MediaItem(
    val id: String,
    val name: String,
    val path: String,
    val uri: String,
    val mimeType: String,
    val size: Long,
    val dateAdded: Long,
    val dateModified: Long,
    val width: Int = 0,
    val height: Int = 0,
    val duration: Long = 0, // For videos, in milliseconds
    val albumId: String? = null,
    val albumName: String? = null,
    val isVideo: Boolean = mimeType.startsWith("video/"),
    val thumbnailPath: String? = null,
    val isCloudItem: Boolean = false,
    val cloudUrl: String? = null,
    val isSynced: Boolean = false
) {
    val aspectRatio: Float
        get() = if (height > 0) width.toFloat() / height.toFloat() else 1f
    
    val formattedSize: String
        get() = formatFileSize(size)
    
    val formattedDuration: String
        get() = if (isVideo && duration > 0) {
            val seconds = duration / 1000
            val minutes = seconds / 60
            val remainingSeconds = seconds % 60
            String.format("%02d:%02d", minutes, remainingSeconds)
        } else ""
}

private fun formatFileSize(bytes: Long): String {
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    var size = bytes.toDouble()
    var unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.size - 1) {
        size /= 1024
        unitIndex++
    }
    
    return String.format("%.1f %s", size, units[unitIndex])
}
