package com.alpha.gallery.core.database.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.alpha.gallery.core.common.Constants
import com.alpha.gallery.core.database.converter.Converters
import com.alpha.gallery.core.database.dao.MediaItemDao
import com.alpha.gallery.core.database.dao.SyncInfoDao
import com.alpha.gallery.core.database.entity.MediaItemEntity
import com.alpha.gallery.core.database.entity.SyncInfoEntity

/**
 * Room database for the Gallery app
 */
@Database(
    entities = [
        MediaItemEntity::class,
        SyncInfoEntity::class
    ],
    version = Constants.DATABASE_VERSION,
    exportSchema = true
)
@TypeConverters(Converters::class)
abstract class GalleryDatabase : RoomDatabase() {
    
    abstract fun mediaItemDao(): MediaItemDao
    abstract fun syncInfoDao(): SyncInfoDao
    
    companion object {
        @Volatile
        private var INSTANCE: GalleryDatabase? = null
        
        fun getDatabase(context: Context): GalleryDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    GalleryDatabase::class.java,
                    Constants.DATABASE_NAME
                )
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
