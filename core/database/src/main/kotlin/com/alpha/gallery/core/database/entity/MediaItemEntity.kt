package com.alpha.gallery.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * Room entity for media items stored in local database
 */
@Entity(
    tableName = "media_items",
    indices = [
        Index(value = ["media_store_id"], unique = true),
        Index(value = ["date_modified"]),
        Index(value = ["date_added"]),
        Index(value = ["mime_type"])
    ]
)
data class MediaItemEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "media_store_id")
    val mediaStoreId: Long,
    
    @ColumnInfo(name = "uri")
    val uri: String,
    
    @ColumnInfo(name = "display_name")
    val displayName: String,
    
    @ColumnInfo(name = "mime_type")
    val mimeType: String,
    
    @ColumnInfo(name = "size")
    val size: Long,
    
    @ColumnInfo(name = "date_added")
    val dateAdded: Long,
    
    @ColumnInfo(name = "date_modified")
    val dateModified: Long,
    
    @ColumnInfo(name = "width")
    val width: Int = 0,
    
    @ColumnInfo(name = "height")
    val height: Int = 0,
    
    @ColumnInfo(name = "duration")
    val duration: Long = 0, // For videos, in milliseconds
    
    @ColumnInfo(name = "album_id")
    val albumId: String? = null,
    
    @ColumnInfo(name = "album_name")
    val albumName: String? = null,
    
    @ColumnInfo(name = "is_favorite")
    val isFavorite: Boolean = false,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis()
) {
    val isVideo: Boolean
        get() = mimeType.startsWith("video/")
    
    val isImage: Boolean
        get() = mimeType.startsWith("image/")
}
