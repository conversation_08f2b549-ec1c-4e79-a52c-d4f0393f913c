package com.alpha.gallery.core.database.dao

import androidx.room.*
import com.alpha.gallery.core.database.entity.MediaItemEntity
import kotlinx.coroutines.flow.Flow

/**
 * DAO for media items with comprehensive batch operations and sync support
 */
@Dao
interface MediaItemDao {
    
    /**
     * Get all media items ordered by date modified descending
     */
    @Query("SELECT * FROM media_items ORDER BY date_modified DESC")
    fun getAllMediaItems(): Flow<List<MediaItemEntity>>
    
    /**
     * Get media items by album
     */
    @Query("SELECT * FROM media_items WHERE album_id = :albumId ORDER BY date_modified DESC")
    fun getMediaItemsByAlbum(albumId: String): Flow<List<MediaItemEntity>>
    
    /**
     * Get media item by MediaStore ID
     */
    @Query("SELECT * FROM media_items WHERE media_store_id = :mediaStoreId LIMIT 1")
    suspend fun getMediaItemByMediaStoreId(mediaStoreId: Long): MediaItemEntity?
    
    /**
     * Get media item by internal ID
     */
    @Query("SELECT * FROM media_items WHERE id = :id LIMIT 1")
    suspend fun getMediaItemById(id: Long): MediaItemEntity?
    
    /**
     * Get all MediaStore IDs for deletion detection
     */
    @Query("SELECT media_store_id FROM media_items")
    suspend fun getAllMediaStoreIds(): List<Long>
    
    /**
     * Get maximum date modified timestamp for incremental sync
     */
    @Query("SELECT MAX(date_modified) FROM media_items")
    suspend fun getMaxDateModified(): Long?
    
    /**
     * Get recent media items with limit
     */
    @Query("SELECT * FROM media_items ORDER BY date_added DESC LIMIT :limit")
    fun getRecentMediaItems(limit: Int): Flow<List<MediaItemEntity>>
    
    /**
     * Get favorite media items
     */
    @Query("SELECT * FROM media_items WHERE is_favorite = 1 ORDER BY date_modified DESC")
    fun getFavoriteMediaItems(): Flow<List<MediaItemEntity>>
    
    /**
     * Search media items by display name
     */
    @Query("SELECT * FROM media_items WHERE display_name LIKE '%' || :query || '%' ORDER BY date_modified DESC")
    fun searchMediaItems(query: String): Flow<List<MediaItemEntity>>
    
    /**
     * Insert single media item with conflict resolution
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMediaItem(mediaItem: MediaItemEntity): Long
    
    /**
     * Insert multiple media items in batch
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMediaItems(mediaItems: List<MediaItemEntity>): List<Long>
    
    /**
     * Update media item
     */
    @Update
    suspend fun updateMediaItem(mediaItem: MediaItemEntity)
    
    /**
     * Update multiple media items in batch
     */
    @Update
    suspend fun updateMediaItems(mediaItems: List<MediaItemEntity>)
    
    /**
     * Delete media item by MediaStore ID
     */
    @Query("DELETE FROM media_items WHERE media_store_id = :mediaStoreId")
    suspend fun deleteByMediaStoreId(mediaStoreId: Long): Int
    
    /**
     * Delete multiple media items by MediaStore IDs
     */
    @Query("DELETE FROM media_items WHERE media_store_id IN (:mediaStoreIds)")
    suspend fun deleteByMediaStoreIds(mediaStoreIds: List<Long>): Int
    
    /**
     * Delete all media items (for full resync)
     */
    @Query("DELETE FROM media_items")
    suspend fun deleteAll()
    
    /**
     * Update favorite status
     */
    @Query("UPDATE media_items SET is_favorite = :isFavorite, updated_at = :timestamp WHERE media_store_id = :mediaStoreId")
    suspend fun updateFavoriteStatus(mediaStoreId: Long, isFavorite: Boolean, timestamp: Long = System.currentTimeMillis())
    
    /**
     * Get total count of media items
     */
    @Query("SELECT COUNT(*) FROM media_items")
    suspend fun getMediaItemCount(): Int
    
    /**
     * Get count by media type
     */
    @Query("SELECT COUNT(*) FROM media_items WHERE mime_type LIKE :mimeTypePrefix || '%'")
    suspend fun getCountByMimeType(mimeTypePrefix: String): Int
    
    /**
     * Custom upsert operation for sync
     */
    @Transaction
    suspend fun upsertMediaItem(mediaItem: MediaItemEntity): Long {
        val existing = getMediaItemByMediaStoreId(mediaItem.mediaStoreId)
        return if (existing != null) {
            // Update existing item with new timestamp
            val updated = mediaItem.copy(
                id = existing.id,
                isFavorite = existing.isFavorite, // Preserve favorite status
                updatedAt = System.currentTimeMillis()
            )
            updateMediaItem(updated)
            existing.id
        } else {
            // Insert new item
            insertMediaItem(mediaItem)
        }
    }
    
    /**
     * Batch upsert operation for efficient sync
     */
    @Transaction
    suspend fun upsertMediaItems(mediaItems: List<MediaItemEntity>): List<Long> {
        val results = mutableListOf<Long>()
        for (mediaItem in mediaItems) {
            results.add(upsertMediaItem(mediaItem))
        }
        return results
    }
}
