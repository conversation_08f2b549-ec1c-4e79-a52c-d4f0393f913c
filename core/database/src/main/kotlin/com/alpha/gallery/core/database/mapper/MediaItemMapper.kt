package com.alpha.gallery.core.database.mapper

import com.alpha.gallery.core.database.entity.MediaItemEntity
import com.alpha.gallery.core.domain.model.MediaItem

/**
 * Mapper between domain MediaItem and database MediaItemEntity
 */
object MediaItemMapper {
    
    /**
     * Convert database entity to domain model
     */
    fun MediaItemEntity.toDomainModel(): MediaItem {
        return MediaItem(
            id = mediaStoreId.toString(),
            name = displayName,
            path = "", // Path is not stored in database, derived from URI
            uri = uri,
            mimeType = mimeType,
            size = size,
            dateAdded = dateAdded,
            dateModified = dateModified,
            width = width,
            height = height,
            duration = duration,
            albumId = albumId,
            albumName = albumName,
            isVideo = isVideo,
            thumbnailPath = null, // Thumbnails are generated on demand
            isCloudItem = false, // Local items only
            cloudUrl = null,
            isSynced = true // Items in database are considered synced
        )
    }
    
    /**
     * Convert domain model to database entity
     */
    fun MediaItem.toEntity(mediaStoreId: Long): MediaItemEntity {
        return MediaItemEntity(
            mediaStoreId = mediaStoreId,
            uri = uri,
            displayName = name,
            mimeType = mimeType,
            size = size,
            dateAdded = dateAdded,
            dateModified = dateModified,
            width = width,
            height = height,
            duration = duration,
            albumId = albumId,
            albumName = albumName,
            isFavorite = false, // Default to not favorite
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * Convert list of entities to domain models
     */
    fun List<MediaItemEntity>.toDomainModels(): List<MediaItem> {
        return map { it.toDomainModel() }
    }
    
    /**
     * Convert list of domain models to entities
     */
    fun List<MediaItem>.toEntities(mediaStoreIds: List<Long>): List<MediaItemEntity> {
        require(size == mediaStoreIds.size) { "MediaItem list and MediaStore ID list must have the same size" }
        return mapIndexed { index, mediaItem ->
            mediaItem.toEntity(mediaStoreIds[index])
        }
    }
    
    /**
     * Create MediaItemEntity from MediaStore cursor data
     */
    fun createFromMediaStore(
        mediaStoreId: Long,
        uri: String,
        displayName: String,
        mimeType: String,
        size: Long,
        dateAdded: Long,
        dateModified: Long,
        width: Int = 0,
        height: Int = 0,
        duration: Long = 0,
        albumId: String? = null,
        albumName: String? = null
    ): MediaItemEntity {
        return MediaItemEntity(
            mediaStoreId = mediaStoreId,
            uri = uri,
            displayName = displayName,
            mimeType = mimeType,
            size = size,
            dateAdded = dateAdded,
            dateModified = dateModified,
            width = width,
            height = height,
            duration = duration,
            albumId = albumId,
            albumName = albumName,
            isFavorite = false,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
    }
}
