package com.alpha.gallery.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Room entity for storing sync metadata and timestamps
 */
@Entity(tableName = "sync_info")
data class SyncInfoEntity(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "last_sync_timestamp")
    val lastSyncTimestamp: Long,
    
    @ColumnInfo(name = "last_full_sync_timestamp")
    val lastFullSyncTimestamp: Long = 0,
    
    @ColumnInfo(name = "sync_count")
    val syncCount: Int = 0,
    
    @ColumnInfo(name = "last_sync_duration_ms")
    val lastSyncDurationMs: Long = 0,
    
    @ColumnInfo(name = "items_added_last_sync")
    val itemsAddedLastSync: Int = 0,
    
    @ColumnInfo(name = "items_updated_last_sync")
    val itemsUpdatedLastSync: Int = 0,
    
    @ColumnInfo(name = "items_deleted_last_sync")
    val itemsDeletedLastSync: Int = 0,
    
    @ColumnInfo(name = "last_error_message")
    val lastErrorMessage: String? = null,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis()
) {
    companion object {
        const val MEDIA_SYNC_ID = "media_sync"
        
        fun createDefault(): SyncInfoEntity {
            return SyncInfoEntity(
                id = MEDIA_SYNC_ID,
                lastSyncTimestamp = 0L
            )
        }
    }
}
