package com.alpha.gallery.core.database.dao

import androidx.room.*
import com.alpha.gallery.core.database.entity.SyncInfoEntity
import kotlinx.coroutines.flow.Flow

/**
 * DAO for sync information and metadata
 */
@Dao
interface SyncInfoDao {
    
    /**
     * Get sync info by ID
     */
    @Query("SELECT * FROM sync_info WHERE id = :id LIMIT 1")
    suspend fun getSyncInfo(id: String): SyncInfoEntity?
    
    /**
     * Get sync info as Flow for reactive updates
     */
    @Query("SELECT * FROM sync_info WHERE id = :id LIMIT 1")
    fun getSyncInfoFlow(id: String): Flow<SyncInfoEntity?>
    
    /**
     * Get media sync info
     */
    suspend fun getMediaSyncInfo(): SyncInfoEntity? {
        return getSyncInfo(SyncInfoEntity.MEDIA_SYNC_ID)
    }
    
    /**
     * Get media sync info as Flow
     */
    fun getMediaSyncInfoFlow(): Flow<SyncInfoEntity?> {
        return getSyncInfoFlow(SyncInfoEntity.MEDIA_SYNC_ID)
    }
    
    /**
     * Insert or update sync info
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSyncInfo(syncInfo: SyncInfoEntity)
    
    /**
     * Update sync info
     */
    @Update
    suspend fun updateSyncInfo(syncInfo: SyncInfoEntity)
    
    /**
     * Update last sync timestamp
     */
    @Query("UPDATE sync_info SET last_sync_timestamp = :timestamp, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateLastSyncTimestamp(id: String, timestamp: Long, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Update sync statistics
     */
    @Query("""
        UPDATE sync_info 
        SET last_sync_timestamp = :timestamp,
            sync_count = sync_count + 1,
            last_sync_duration_ms = :durationMs,
            items_added_last_sync = :itemsAdded,
            items_updated_last_sync = :itemsUpdated,
            items_deleted_last_sync = :itemsDeleted,
            last_error_message = :errorMessage,
            updated_at = :updatedAt
        WHERE id = :id
    """)
    suspend fun updateSyncStatistics(
        id: String,
        timestamp: Long,
        durationMs: Long,
        itemsAdded: Int,
        itemsUpdated: Int,
        itemsDeleted: Int,
        errorMessage: String? = null,
        updatedAt: Long = System.currentTimeMillis()
    )
    
    /**
     * Update full sync timestamp
     */
    @Query("UPDATE sync_info SET last_full_sync_timestamp = :timestamp, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateLastFullSyncTimestamp(id: String, timestamp: Long, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Clear error message
     */
    @Query("UPDATE sync_info SET last_error_message = NULL, updated_at = :updatedAt WHERE id = :id")
    suspend fun clearErrorMessage(id: String, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Delete sync info by ID
     */
    @Query("DELETE FROM sync_info WHERE id = :id")
    suspend fun deleteSyncInfo(id: String)
    
    /**
     * Get all sync info entries
     */
    @Query("SELECT * FROM sync_info ORDER BY updated_at DESC")
    suspend fun getAllSyncInfo(): List<SyncInfoEntity>
    
    /**
     * Initialize media sync info if not exists
     */
    @Transaction
    suspend fun initializeMediaSyncInfo(): SyncInfoEntity {
        val existing = getMediaSyncInfo()
        return if (existing != null) {
            existing
        } else {
            val defaultSyncInfo = SyncInfoEntity.createDefault()
            insertSyncInfo(defaultSyncInfo)
            defaultSyncInfo
        }
    }
}
