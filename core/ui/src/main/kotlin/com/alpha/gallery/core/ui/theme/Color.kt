package com.alpha.gallery.core.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Gallery specific colors
val GalleryPrimary = Color(0xFF1976D2)
val GalleryPrimaryVariant = Color(0xFF1565C0)
val GallerySecondary = Color(0xFF03DAC6)
val GallerySecondaryVariant = Color(0xFF018786)

val GalleryBackground = Color(0xFFFAFAFA)
val GalleryBackgroundDark = Color(0xFF121212)
val GallerySurface = Color(0xFFFFFFFF)
val GallerySurfaceDark = Color(0xFF1E1E1E)

val GalleryOnPrimary = Color(0xFFFFFFFF)
val GalleryOnSecondary = Color(0xFF000000)
val GalleryOnBackground = Color(0xFF000000)
val GalleryOnBackgroundDark = Color(0xFFFFFFFF)
val GalleryOnSurface = Color(0xFF000000)
val GalleryOnSurfaceDark = Color(0xFFFFFFFF)

val GalleryError = Color(0xFFB00020)
val GalleryOnError = Color(0xFFFFFFFF)
