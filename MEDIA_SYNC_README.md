# Media Library Synchronization System

## Overview

This document describes the comprehensive media library synchronization system implemented for the Gallery app. The system periodically scans device images and videos, imports their metadata into the app's internal Room database, and maintains incremental synchronization with the MediaStore.

## Architecture

### Core Components

1. **Database Layer** (`core/database`)
   - `MediaItemEntity`: Room entity for storing media metadata
   - `SyncInfoEntity`: Entity for tracking sync state and statistics
   - `MediaItemDao`: DAO with batch operations and incremental sync queries
   - `SyncInfoDao`: DAO for sync metadata management

2. **Sync Layer** (`core/sync`)
   - `SyncManager`: Orchestrates synchronization process
   - `MediaStoreScanner`: Queries MediaStore for images and videos
   - `SyncWorker`: WorkManager worker for background sync
   - `SyncScheduler`: Manages periodic and immediate sync scheduling
   - `MediaStoreObserver`: ContentObserver for real-time change detection
   - `PermissionUtils`: Handles runtime permissions for API 31+

3. **Repository Layer** (`data/local`)
   - `MediaRepositoryImpl`: Implements domain repository interface
   - Coordinates between database and sync operations

## Key Features

### 1. Permissions & Scoped Storage (API 31+)
- **Runtime Permissions**: 
  - API ≤32: `READ_EXTERNAL_STORAGE`
  - API 33+: `READ_MEDIA_IMAGES` and `READ_MEDIA_VIDEO`
- **Graceful Handling**: Permission checking before MediaStore queries
- **Error Reporting**: Proper error handling for permission denial

### 2. Database Design
- **MediaItemEntity**: Stores complete media metadata with indexes
- **SyncInfoEntity**: Tracks sync timestamps and statistics
- **Batch Operations**: Efficient upsert operations with conflict resolution
- **Incremental Queries**: Timestamp-based queries for incremental sync

### 3. Synchronization Strategy
- **Initial Full Sync**: Complete scan when database is empty
- **Incremental Sync**: Query only modified items since last sync
- **Deletion Detection**: Periodic comparison to detect removed files
- **Batch Processing**: Process large datasets in configurable batches

### 4. Background Execution
- **WorkManager**: Periodic sync every 6 hours with constraints
- **ContentObserver**: Real-time MediaStore change detection
- **Debounced Triggers**: Avoid excessive sync operations
- **Error Recovery**: Retry logic for transient failures

### 5. Performance Optimizations
- **Pagination**: Process large datasets in batches (500-1000 items)
- **Transactions**: Wrap batch operations in database transactions
- **Resource Management**: Proper cursor lifecycle management
- **Memory Efficiency**: Avoid loading entire datasets into memory

## Usage

### Automatic Initialization
The sync system initializes automatically when the app starts:

```kotlin
@HiltAndroidApp
class GalleryApplication : Application() {
    @Inject
    lateinit var syncInitializer: SyncInitializer
    
    override fun onCreate() {
        super.onCreate()
        syncInitializer.initialize()
    }
}
```

### Manual Sync Operations
```kotlin
// Inject MediaRepository
@Inject
lateinit var mediaRepository: MediaRepository

// Trigger immediate sync
mediaRepository.refreshMediaItems()

// Get sync status
val status = (mediaRepository as MediaRepositoryImpl).getSyncStatus()
```

### Permission Handling
```kotlin
@Inject
lateinit var permissionUtils: PermissionUtils

// Check permissions
if (permissionUtils.hasAllPermissions()) {
    // Sync can proceed
} else {
    // Request missing permissions
    val missing = permissionUtils.getMissingPermissions()
}
```

## Configuration

### Constants (in `core/common/Constants.kt`)
```kotlin
const val SYNC_WORK_NAME = "media_sync_work"
const val SYNC_BATCH_SIZE = 500
const val SYNC_INTERVAL_HOURS = 6L
const val MEDIASTORE_BATCH_SIZE = 1000
```

### Sync Intervals
- **Periodic Sync**: Every 6 hours
- **Full Sync**: Weekly or after errors
- **Real-time**: ContentObserver with 2-second debounce

## Testing

### Unit Tests
- `SyncManagerTest`: Tests sync logic and error handling
- Mock dependencies for isolated testing
- Verify permission checks and sync statistics

### Integration Testing
Run on real device with actual media files to test:
- Large dataset handling
- Permission scenarios
- Background sync behavior

## Error Handling

### Comprehensive Exception Handling
- SecurityException for permission issues
- IllegalArgumentException for invalid queries
- Proper cursor resource management
- Detailed logging for debugging

### Recovery Mechanisms
- Retry logic for transient errors
- Full sync fallback for corruption
- Error state tracking in sync metadata

## Performance Considerations

### Memory Management
- Process datasets in configurable batches
- Proper cursor lifecycle management
- Avoid OOM with large media libraries

### Database Optimization
- Indexes on frequently queried columns
- Batch transactions for bulk operations
- Efficient upsert operations

### Background Processing
- WorkManager constraints (battery, storage)
- Debounced ContentObserver triggers
- Minimal main thread usage

## Dependencies

### Required Libraries
- Room database with KTX extensions
- WorkManager with Hilt integration
- Kotlin coroutines for async operations
- Hilt for dependency injection

### Permissions (AndroidManifest.xml)
```xml
<!-- API 32 and below -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
    android:maxSdkVersion="32" />

<!-- API 33+ -->
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
```

## Future Enhancements

1. **Album Aggregation**: Implement album creation from media items
2. **Cloud Sync**: Add cloud storage synchronization
3. **Thumbnail Generation**: Cache thumbnails for performance
4. **Metadata Extraction**: Extract EXIF data and other metadata
5. **Conflict Resolution**: Handle concurrent modifications
6. **Progress Reporting**: Optional UI progress display
7. **Selective Sync**: Allow users to choose sync folders

## Troubleshooting

### Common Issues
1. **Permission Denied**: Check runtime permissions for API level
2. **Empty Results**: Verify MediaStore accessibility
3. **Performance Issues**: Adjust batch sizes for device capabilities
4. **Sync Not Running**: Check WorkManager constraints and scheduling

### Debugging
- Enable detailed logging with `Log.d` statements
- Monitor WorkManager status in device settings
- Check database content with Room inspector
- Verify ContentObserver registration

This implementation provides a robust, efficient, and maintainable media synchronization system that handles the complexities of modern Android storage access while maintaining excellent performance and user experience.
